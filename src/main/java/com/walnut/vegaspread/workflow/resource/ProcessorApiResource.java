package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.entity.IdentifierKeyword;
import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.JobExecutionDto;
import com.walnut.vegaspread.workflow.model.UpdateDocDto;
import com.walnut.vegaspread.workflow.model.UploadItemSchema;
import com.walnut.vegaspread.workflow.service.DocumentService;
import com.walnut.vegaspread.workflow.service.JobExecutionMonitoringService;
import com.walnut.vegaspread.workflow.service.KwService;
import com.walnut.vegaspread.workflow.service.TaskService;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.OpenAPIDefinition;
import org.eclipse.microprofile.openapi.annotations.info.Info;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.RestForm;
import org.jboss.resteasy.reactive.RestQuery;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.walnut.vegaspread.workflow.utils.Config.WALNUT_AI_USER;

@OpenAPIDefinition(info = @Info(title = "Processor API authenticated by X-API-Key", version = "1.0"))
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/wise/processor")
@ApiKeyAuthenticate
public class ProcessorApiResource {
    private static final String DEBUG_PATH = "debug/%s/%s";
    private static final String DOCUMENT_PREFIX = "/document";
    private static final String SPREAD_PREFIX = "/spread";
    private static final String JOB_EXECUTION_MONITORING_PREFIX = "/job-execution-monitoring";

    private final DocumentService documentService;
    private final KwService kwService;
    private final TaskService taskService;
    private final CloudProvider cloudProvider;
    private final JobExecutionMonitoringService jobExecutionMonitoringService;

    public ProcessorApiResource(DocumentService documentService, KwService kwService, TaskService taskService,
                                CloudProviderFactory cloudProviderFactory,
                                JobExecutionMonitoringService jobExecutionMonitoringService) {
        this.documentService = documentService;
        this.kwService = kwService;
        this.taskService = taskService;
        this.cloudProvider = cloudProviderFactory.getCloudProvider();
        this.jobExecutionMonitoringService = jobExecutionMonitoringService;
    }

    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.TEXT_PLAIN)
    @Path("/doc/debug/{docId}")
    @PUT
    public String uploadDebugFile(@PathParam("docId") UUID docId,
                                  @RestForm("file") @Schema(implementation = UploadItemSchema.class) FileUpload fileUpload) throws IOException {
        String objectPath = String.format(DEBUG_PATH, docId, fileUpload.fileName());
        cloudProvider.upload(objectPath, fileUpload.uploadedFile());
        return objectPath;
    }

    @Path("/doc/debug/upload-url/{docId}")
    @GET
    public UrlDto uploadDebugFile(@PathParam("docId") UUID docId, @RestQuery String fileName) {
        String objectPath = String.format(DEBUG_PATH, docId, fileName);
        return new UrlDto(cloudProvider.getSignedPutUrl(objectPath, 3, "application/octet-stream"));
    }

    @Path("/doc/debug/link/{docId}")
    @GET
    public UrlDto getDebugLink(@PathParam("docId") UUID docId, @NotNull @RestQuery("filename") String filename) {
        String path = String.format(DEBUG_PATH, docId, filename);
        return new UrlDto(cloudProvider.getLink(path, 2));
    }

    @Path("/keyword/{category}")
    @GET
    public List<IdentifierKeyword> listKeywordsForCategory(@PathParam("category") String category) {
        return kwService.list(category);
    }

    @Path(DOCUMENT_PREFIX + "/{docId}")
    @PATCH
    public DocOutputDto updateDocumentMetadata(@PathParam("docId") UUID docId, UpdateDocDto docDto) {
        return documentService.updateInDbByProcessor(docId, docDto, WALNUT_AI_USER);
    }

    @Path(DOCUMENT_PREFIX + "/{docId}")
    @GET
    public DocOutputDto getDocFromDb(@PathParam("docId") UUID docId) {
        return documentService.docToOutputForApi(documentService.getFromDb(docId));
    }

    @Path(DOCUMENT_PREFIX + "/link/{docId}")
    @GET
    public UrlDto getDocLink(@PathParam("docId") UUID docId, @QueryParam("fileType") String fileType) {
        return documentService.getSignedUrl(docId, fileType);
    }

    @Path(DOCUMENT_PREFIX + "/filter")
    @GET
    public List<UUID> filterDocuments(@NotNull @QueryParam("filterColumn") String filterColumn,
                                      @NotNull @QueryParam("filterValue") Integer filterValue,
                                      @QueryParam("clientName") String clientName) {
        return documentService.filterDocuments(filterColumn, filterValue, clientName);
    }

    @Path(SPREAD_PREFIX + "/{spreadId}")
    @GET
    public DocOutputDto getSpread(@PathParam("spreadId") Integer spreadId) {
        SpreadingTask task = taskService.get(spreadId);
        if (task == null) {
            throw new IllegalArgumentException(String.format("Spread %s does not exist", spreadId));
        }
        return documentService.docToOutputForApi(documentService.getFromDb(task.documents.get(0).docId));
    }

    @Path(JOB_EXECUTION_MONITORING_PREFIX + "/start-job")
    @POST
    public JobExecutionDto.Response auditJobStatusForStartJob(JobExecutionDto.StartJob jobExecutionStartDto) {
        return JobExecutionMonitoring.toDto(jobExecutionMonitoringService.auditStartJob(jobExecutionStartDto));
    }

    @Path(JOB_EXECUTION_MONITORING_PREFIX + "/end-job")
    @POST
    public JobExecutionDto.Response auditJobStatusForEndJob(JobExecutionDto.EndJob jobExecutionEndDto) {
        return Optional.ofNullable(jobExecutionMonitoringService.auditEndJob(jobExecutionEndDto))
                .map(JobExecutionMonitoring::toDto)
                .orElse(null);
    }
}
