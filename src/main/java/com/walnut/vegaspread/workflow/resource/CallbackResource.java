package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.model.ApiKeyCredentialsDto;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.service.DocumentService;
import com.walnut.vegaspread.workflow.service.WiseWorkflowService;
import com.walnut.vegaspread.workflow.utils.Config;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.openapi.annotations.enums.ParameterIn;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;

import java.net.URI;
import java.util.UUID;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/callback")
@ApiKeyAuthenticate
public class CallbackResource {
    private static final Logger logger = Logger.getLogger(CallbackResource.class);

    private final DocumentService documentService;
    private final CloudProviderFactory cloudProviderFactory;
    private final String envName;
    private final String cloudProviderType;
    @ConfigProperty(name = Config.ROTATION_RETRY_COUNT)
    int rotationRetryCount;
    @ConfigProperty(name = Config.OCR_RETRY_COUNT)
    int ocrRetryCount;
    @ConfigProperty(name = Config.DOC_AI_RETRY_COUNT)
    int docAiRetryCount;
    @ConfigProperty(name = Config.FS_CLF_RETRY_COUNT)
    int fsClfRetryCount;
    @ConfigProperty(name = Config.PROCESSOR_RETRY_COUNT)
    int processorRetryCount;

    public CallbackResource(DocumentService documentService,
                            CloudProviderFactory cloudProviderFactory,
                            @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType,
                            @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY) String envName) {
        this.documentService = documentService;
        this.cloudProviderFactory = cloudProviderFactory;
        this.cloudProviderType = cloudProviderType;
        this.envName = envName;
    }

    @POST
    @Path("/{docId}")
    public RestResponse<Void> callback(@PathParam("docId") UUID docId,
                                       WiseCallbackDto.CbMetadata cbMetadata,
                                       @Parameter(name = "X-CLIENT-ID", in = ParameterIn.HEADER, required = true,
                                               description = "Client ID")
                                       @HeaderParam("X-CLIENT-ID") String clientId,
                                       @Parameter(name = "X-API-KEY", in = ParameterIn.HEADER, required = true,
                                               description = "API Key")
                                       @HeaderParam("X-API-KEY") String apiKey) {
        logger.debugf("Received callback for docId: %s, metadata: %s", docId, cbMetadata);
        logger.debugf("Cloud Provider Type : %s", cloudProviderType);
        logger.debugf("Environment Name : %s", envName);
        DocOutputDto document = documentService.updateDocStatus(cbMetadata.getDocId(), StatusEnum.PROCESSING,
                cbMetadata.getCbStage().toString());

        if (document == null) {
            logger.errorf("Document not found for docId: %s", docId);
            return RestResponse.notFound();
        }

        // Get the appropriate cloud provider from the factory
        CloudProvider cloudProvider = cloudProviderFactory.getCloudProvider();

        URI serviceBaseUrl = cloudProvider.buildServiceBaseUrl();

        StageEnum invokingStage = cbMetadata.getErrorStage();
        StageEnum currentCallbackStage = cbMetadata.getCbStage();

        if (cbMetadata.getRetryCount() > 0) {
            logger.errorf("Error in processing document %s, error: %s", docId, cbMetadata.getErrorMessage());
            if (isMaxRetry(cbMetadata.getRetryCount(), cbMetadata.getCbStage())) {
                documentService.updateDocStatus(docId, StatusEnum.FAILED,
                        String.format("FAILED_%s", cbMetadata.getCbStage()));
                return RestResponse.ok();
            }
            //Re-trigger current stage.
            cbMetadata.setCbStage(invokingStage);
        } else {
            //Set next stage for the callback stage in the invoking task.
            cbMetadata.setCbStage(getNextStage(currentCallbackStage));
        }
        cbMetadata.setErrorStage(currentCallbackStage);

        // Create workflow service with the cloud provider
        WiseWorkflowService wiseWorkflowService = new WiseWorkflowService(cbMetadata,
                new ApiKeyCredentialsDto(clientId, apiKey), serviceBaseUrl, cloudProvider, envName,
                document);

        wiseWorkflowService.handleCallback(currentCallbackStage);
        return RestResponse.ok();
    }

    private boolean isMaxRetry(int retryCount, StageEnum stage) {
        int maxRetries = switch (stage) {
            case ROTATION -> rotationRetryCount;
            case OCR -> ocrRetryCount;
            case DOC_AI -> docAiRetryCount;
            case FS_CLF -> fsClfRetryCount;
            case PROCESS -> processorRetryCount;
            default -> 0;
        };
        return retryCount >= maxRetries;
    }

    private StageEnum getNextStage(StageEnum processStage) {
        return switch (processStage) {
            case ROTATION -> StageEnum.OCR;
            case OCR -> StageEnum.DOC_AI;
            case DOC_AI -> StageEnum.FS_CLF;
            case FS_CLF -> StageEnum.PROCESS;
            default -> {
                logger.errorf("Invalid process stage for next step: %s", processStage);
                throw new IllegalArgumentException("Invalid process stage for next step: " + processStage);
            }
        };
    }
}
