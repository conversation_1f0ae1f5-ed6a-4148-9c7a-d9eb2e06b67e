package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.IdentifierKeyword;
import com.walnut.vegaspread.workflow.model.KeywordDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.model.UploadItemSchema;
import com.walnut.vegaspread.workflow.service.ExchangeService;
import com.walnut.vegaspread.workflow.service.KwService;
import com.walnut.vegaspread.workflow.service.ProcessorService;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.RestForm;
import org.jboss.resteasy.reactive.RestResponse;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.walnut.vegaspread.common.utils.Constants.DEFAULT_CLIENT_NAME;
import static com.walnut.vegaspread.common.utils.Constants.SUMMARY_TEMPLATE_EXCEL_PATH;
import static com.walnut.vegaspread.common.utils.Jwt.getUsername;
import static com.walnut.vegaspread.workflow.utils.Utils.cleanFilenameString;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/processor")
@Authenticated
public class ProcessorResource {
    private static final String XLSX_MIME_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private final JsonWebToken accessToken;
    private final ProcessorService processorService;
    private final KwService kwService;
    private final ExchangeService exchangeService;
    private final CloudProvider cloudProvider;

    public ProcessorResource(JsonWebToken accessToken,
                             ProcessorService processorService,
                             KwService kwService,
                             ExchangeService exchangeService,
                             CloudProviderFactory cloudProviderFactory) {
        this.accessToken = accessToken;
        this.processorService = processorService;
        this.kwService = kwService;
        this.exchangeService = exchangeService;
        this.cloudProvider = cloudProviderFactory.getCloudProvider();
    }

    @Path("/process/doc/{docId}")
    @POST
    @RolesAllowed(Roles.UPLOAD_FILE)
    public RestResponse<Void> startProcess(@PathParam("docId") UUID docId,
                                           @DefaultValue("ROTATION") @QueryParam("processStage") StageEnum processStage) {
        URI baseUri = cloudProvider.buildServiceBaseUrl();
        Response.Status status = processorService.startProcess(docId, processStage, baseUri);
        return RestResponse.status(status);
    }

    @Path("/process/doc/{docId}/block/{blockId}")
    @POST
    @RolesAllowed(Roles.MAP_COA)
    public RestResponse<Void> startProcess(@PathParam("docId") UUID docId,
                                           @PathParam("blockId") Integer blockId,
                                           @Context UriInfo uriInfo) throws InterruptedException {
        URI baseUri = uriInfo.getBaseUri();
        Response.Status status = processorService.processBlock(docId, baseUri, blockId);
        return RestResponse.status(status);
    }

    @Path("/doc/summary/template/excel/link/{clientName}")
    @GET
    public UrlDto getSummaryTemplateLink(@PathParam("clientName") String clientName,
                                         @QueryParam("coaClientName") @DefaultValue(DEFAULT_CLIENT_NAME) String coaClientName) {
        String path = String.format(SUMMARY_TEMPLATE_EXCEL_PATH, cleanFilenameString(clientName),
                cleanFilenameString(coaClientName));
        return new UrlDto(cloudProvider.getLink(path, 2));
    }

    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @Path("/doc/summary/template/excel/download/{clientName}")
    @GET
    public byte[] downloadSummaryTemplateExcel(@PathParam("clientName") String clientName,
                                               @QueryParam("coaClientName") @DefaultValue(DEFAULT_CLIENT_NAME) String coaClientName) {
        String gcsPath = String.format(SUMMARY_TEMPLATE_EXCEL_PATH, cleanFilenameString(clientName),
                cleanFilenameString(clientName));
        return cloudProvider.download(gcsPath);
    }

    @Path("/doc/summary/template/excel/upload-url/{clientName}")
    @GET
    @RolesAllowed(Roles.SUPERADMIN)
    public UrlDto uploadSummaryTemplateExcelLink(@PathParam("clientName") String clientName,
                                                 @QueryParam("coaClientName") @DefaultValue(DEFAULT_CLIENT_NAME) String coaClientName) {
        String objectPath = String.format(SUMMARY_TEMPLATE_EXCEL_PATH, cleanFilenameString(clientName),
                cleanFilenameString(coaClientName));
        return new UrlDto(cloudProvider.getSignedPutUrl(objectPath, 3, XLSX_MIME_TYPE));
    }

    @Path("/doc/summary/template/excel/upload/{clientName}")
    @PUT
    @RolesAllowed(Roles.SUPERADMIN)
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.TEXT_PLAIN)
    public RestResponse<String> uploadSummaryTemplateExcel(@PathParam("clientName") String clientName,
                                                           @QueryParam("coaClientName") @DefaultValue(DEFAULT_CLIENT_NAME) String coaClientName,
                                                           @RestForm("summary-file") @Schema(implementation =
                                                                   UploadItemSchema.class) FileUpload file) throws IOException {
        if (!(file.contentType().equalsIgnoreCase(XLSX_MIME_TYPE))) {
            return RestResponse.status(Response.Status.BAD_REQUEST, "File format not supported. Upload an xslx file");
        }
        String objectPath = String.format(SUMMARY_TEMPLATE_EXCEL_PATH, cleanFilenameString(clientName),
                cleanFilenameString(coaClientName));
        cloudProvider.upload(objectPath, file.uploadedFile());
        return RestResponse.status(Response.Status.OK, objectPath);
    }

    @Path("/keyword")
    @POST
    @RolesAllowed(Roles.ADMIN)
    public List<IdentifierKeyword> addKeywords(List<KeywordDto> keywordDtos) {
        String fullName = exchangeService.usernameToName(getUsername(accessToken));
        List<IdentifierKeyword> keywords = keywordDtos.stream()
                .map(kwDto -> IdentifierKeyword.builder()
                        .category(kwDto.category())
                        .kwText(kwDto.kwText())
                        .auditable(new Auditable(getUsername(accessToken)))
                        .build())
                .toList();
        kwService.saveKeywords(keywords);
        keywords.forEach(keyword -> keyword.auditable.createdByFullName = fullName);
        return keywords;
    }

    @Path("/keyword/{category}")
    @GET
    @RolesAllowed(Roles.LABEL_DATA)
    public List<IdentifierKeyword> listKeywordsForCategory(@PathParam("category") String category) {
        List<IdentifierKeyword> identifierKeywords = kwService.list(category);
        Map<String, String> usernameToNames = exchangeService.getUsernameMapping(
                identifierKeywords.stream()
                        .map(identifierKeyword -> identifierKeyword.auditable.createdBy));
        identifierKeywords.forEach(identifierKeyword ->
                identifierKeyword.auditable.createdByFullName = usernameToNames.get(
                        identifierKeyword.auditable.createdBy));
        return identifierKeywords;
    }

    @Path("/re-process")
    @POST
    public void reprocess() {
        //Placeholder to call API from processor to reprocess and generate extracted and transformed data.
    }
}
