package com.walnut.vegaspread.workflow.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.jose4j.json.internal.json_simple.JSONObject;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface WiseCallbackDto {
    String DOC_ID_ARG = "--doc_id";
    String CREATED_TIME_ARG = "--created_time";
    String BUCKET_NAME_ARG = "--bucket_name";
    String IS_DIGITAL_ARG = "--is_digital";
    String CB_DATA_ARG = "--cb_data_base64";
    String USE_TABLES_ARG = "--use_tables";
    String FORCE_SCANNED_ARG = "--force_scanned";
    String DO_LAYOUT_ARG = "--do_layout";
    String DO_TABLE_ARG = "--do_table";

    enum CbMethod {
        POST,
        GET
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    class CbBaseDto {
        private UUID docId;
        private LocalDate createdTime;
        private String bucketName;
        private Boolean isDigital;

        public Map<String, Object> toMap() {
            HashMap<String, Object> map = new HashMap<>();
            map.put("docId", docId);
            map.put("createdTime", createdTime);
            map.put("bucketName", bucketName);
            map.put("isDigital", isDigital);
            return map;
        }

        public String toString() {
            return new JSONObject(toMap()).toString();
        }

        public List<String> generateArgs() {
            return List.of(
                    DOC_ID_ARG, docId.toString(),
                    CREATED_TIME_ARG, createdTime.toString(),
                    BUCKET_NAME_ARG, bucketName,
                    IS_DIGITAL_ARG, Boolean.TRUE.equals(isDigital) ? "1" : "0"
            );
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    class CbMetadata extends CbBaseDto {
        private StageEnum cbStage;
        private int retryCount;
        private String errorMessage;
        private StageEnum errorStage;

        public CbMetadata(UUID docId, LocalDate createdTime, String bucketName, Boolean isDigital, StageEnum cbStage,
                          int retryCount, String errorMessage, StageEnum errorStage) {
            super(docId, createdTime, bucketName, isDigital);
            this.cbStage = cbStage;
            this.retryCount = retryCount;
            this.errorMessage = errorMessage;
            this.errorStage = errorStage;
        }

        @Override
        public Map<String, Object> toMap() {
            HashMap<String, Object> map = new HashMap<>(super.toMap());
            map.put("cbStage", cbStage.toString());
            map.put("retryCount", retryCount);
            map.put("errorMessage", errorMessage);
            map.put("errorStage", errorStage.toString());
            return map;
        }

        @Override
        public String toString() {
            return new JSONObject(toMap()).toString();
        }
    }

    class OcrOrRotationDto extends CbBaseDto {
        private final CallbackData cbData;

        public OcrOrRotationDto(CbBaseDto cbMetadata, CallbackData cbData) {
            super(cbMetadata.getDocId(), cbMetadata.getCreatedTime(), cbMetadata.getBucketName(),
                    cbMetadata.getIsDigital());
            this.cbData = cbData;
        }

        @Override
        public List<String> generateArgs() {
            ArrayList<String> args = new ArrayList<>(super.generateArgs());
            args.addAll(List.of(CB_DATA_ARG, cbData.toBase64String()));
            return args;
        }
    }

    @Getter
    class DocAiDto extends CbBaseDto {
        private final Boolean forceScanned;
        private final Boolean layout;
        private final Boolean table;
        private final CallbackData cbData;
        private final String cbApiKey;

        public DocAiDto(CbBaseDto cbMetadata, Boolean layout, Boolean table, CallbackData cbData, String cbApiKey) {
            super(cbMetadata.getDocId(), cbMetadata.getCreatedTime(), cbMetadata.getBucketName(),
                    cbMetadata.getIsDigital());
            this.forceScanned = !cbMetadata.getIsDigital();
            this.layout = layout;
            this.table = table;
            this.cbData = cbData;
            this.cbApiKey = cbApiKey;
        }

        public JSONObject generatePayload() {
            String currentTimeStr = LocalDateTime.now().toString();
            HashMap<String, Object> data = new HashMap<>();
            data.put("doc_id", super.getDocId());
            data.put("created_time", super.getCreatedTime());
            data.put("bucket_name", super.getBucketName());
            data.put("is_digital", super.getIsDigital());
            data.put("force_scanned", forceScanned);
            data.put("layout", layout);
            data.put("table", table);
            data.put("cb_data", cbData.toBase64String());
            data.put("cb_api_key", cbApiKey);
            return new JSONObject(Map.of("input", data, "id", super.getDocId() + "-" + currentTimeStr));
        }

        @Override
        public List<String> generateArgs() {
            return List.of(
                    DOC_ID_ARG, super.getDocId().toString(),
                    CREATED_TIME_ARG, super.getCreatedTime().toString(),
                    BUCKET_NAME_ARG, super.getBucketName(),
                    FORCE_SCANNED_ARG, forceScanned.toString(),
                    DO_LAYOUT_ARG, layout.toString(),
                    DO_TABLE_ARG, table.toString(),
                    CB_DATA_ARG, cbData.toBase64String()
            );
        }
    }

    class FsClfDto extends CbBaseDto {
        private final CallbackData cbData;
        private final Boolean useTables;

        public FsClfDto(CbBaseDto cbMetadata, Boolean useTables, CallbackData cbData) {
            super(cbMetadata.getDocId(), cbMetadata.getCreatedTime(), cbMetadata.getBucketName(),
                    cbMetadata.getIsDigital());
            this.cbData = cbData;
            this.useTables = useTables;
        }

        @Override
        public List<String> generateArgs() {
            ArrayList<String> args = new ArrayList<>(super.generateArgs());
            args.addAll(List.of(CB_DATA_ARG, cbData.toBase64String(), USE_TABLES_ARG,
                    Boolean.TRUE.equals(useTables) ? "1" : "0"));
            return args;
        }
    }

    @AllArgsConstructor
    class ProcessorDto {
        private final UUID docId;
        private final String clientId;
        private final String clientName;
        private final StageEnum.ReprocessStage reprocessStage;
        private final CallbackData cbData;

        public List<String> generateArgs() {
            return List.of(DOC_ID_ARG, docId.toString(), "--client_id", clientId, "--client_name", clientName,
                    "--process_stage", reprocessStage.toString(), CB_DATA_ARG, cbData.toBase64String());
        }
    }

    class ProcessorBlockDto extends ProcessorDto {
        private final Integer blockId;

        public ProcessorBlockDto(UUID docId, String clientId, String clientName,
                                 StageEnum.ReprocessStage reprocessStage,
                                 Integer blockId) {
            super(docId, clientId, clientName, reprocessStage, null);
            this.blockId = blockId;
        }

        @Override
        public List<String> generateArgs() {
            ArrayList<String> args = new ArrayList<>(super.generateArgs());
            args.add("--block_id");
            args.add(blockId.toString());
            return args;
        }
    }

    record CallbackData(String url, CbMetadata metadata, CbMethod method, String clientId, String startJobTelemetryUrl,
                        String endJobTelemetryUrl) {
        public Map<String, Object> toMap() {
            return Map.of(
                    "url", url,
                    "metadata", metadata.toMap(),
                    "method", method.toString(),
                    "client_id", clientId,
                    "start_job_telemetry_url", startJobTelemetryUrl,
                    "end_job_telemetry_url", endJobTelemetryUrl
            );
        }

        public String toBase64String() {
            String jsonString = new JSONObject(toMap()).toString();
            return Base64.getEncoder().encodeToString(jsonString.getBytes());
        }
    }
}
