package com.walnut.vegaspread.workflow.model;

public enum StageEnum {
    ROTAT<PERSON>,
    OCR,
    DOC_AI,
    FS_CLF,
    PROCESS(ReprocessStage.COMPLETE),
    FINISH_REVIEW(ReprocessStage.REVIEW),
    REPROCESS_COMPLETE(ReprocessStage.COMPLETE),
    REPROCESS_TABLE_TAG(ReprocessStage.TABLE_TAG),
    REPROCESS_COA(ReprocessStage.COA),
    REPROCESS_NTA_BLOCK(ReprocessStage.NTA_BLOCK),
    FINISH_PROCESS;

    private final ReprocessStage reprocessStage;

    StageEnum() {
        this.reprocessStage = null;
    }

    StageEnum(ReprocessStage reprocessStage) {
        this.reprocessStage = reprocessStage;
    }

    public boolean isReprocess() {
        return reprocessStage != null;
    }

    public ReprocessStage getReprocessStage() {
        return reprocessStage;
    }

    public enum ReprocessStage {
        COMPLETE,
        TABLE_TAG,
        COA,
        NTA_BLOCK,
        REVIEW
    }
}
