package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.extraction.DenominationEnum;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.model.CreateTaskDto;
import com.walnut.vegaspread.workflow.model.DocFile;
import com.walnut.vegaspread.workflow.model.MetadataDto;
import com.walnut.vegaspread.workflow.model.SpreadLevelEnum;
import com.walnut.vegaspread.workflow.model.StageEnum;
import jakarta.enterprise.context.ApplicationScoped;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.net.URI;
import java.time.LocalDate;

import static com.walnut.vegaspread.workflow.utils.Config.COMMON_CLIENT_NAME;
import static com.walnut.vegaspread.workflow.utils.Config.WALNUT_AI_USER;

@ApplicationScoped
public class IntegrationService {
    private final DocumentService documentService;
    private final TaskService taskService;
    private final ProcessorService processorService;

    public IntegrationService(DocumentService documentService, TaskService taskService,
                              ProcessorService processorService) {
        this.documentService = documentService;
        this.taskService = taskService;
        this.processorService = processorService;
    }

    private ImmutablePair<String, String> getFileName(String originalFileName, String folderName) {
        String baseName = originalFileName;
        String extension = "";
        int lastDotIndex = originalFileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            baseName = originalFileName.substring(0, lastDotIndex);
            extension = originalFileName.substring(lastDotIndex);
        }
        return new ImmutablePair<>(baseName + "_" + folderName, extension);
    }

    public void processBase64Doc(FileUpload fileUpload, String folderName, URI baseUri) throws IOException {
        ImmutablePair<String, String> fileDetails = getFileName(fileUpload.fileName(), folderName);
        String fileName = fileDetails.getLeft() + fileDetails.getRight();
        DocFile docFile = new DocFile(fileUpload.filePath(), fileName, fileUpload.size());
        Document doc = documentService.uploadDoc(0, docFile, WALNUT_AI_USER, false, null);

        CreateTaskDto taskDto = new CreateTaskDto(LocalDate.of(2023, 12, 31), SpreadLevelEnum.CONSOLIDATED,
                new MetadataDto.Entity(null, fileDetails.getLeft()), new MetadataDto.Industry(null, "NA"),
                new MetadataDto.Region(null, "Philippines"), false, doc.docId, DenominationEnum.NONE,
                DenominationEnum.THOUSAND);
        taskService.create(taskDto, COMMON_CLIENT_NAME, WALNUT_AI_USER);

        processorService.startProcess(doc.docId, StageEnum.ROTATION, baseUri);
    }
}
