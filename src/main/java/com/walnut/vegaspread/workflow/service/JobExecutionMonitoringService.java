package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.workflow.entity.JobExecutionMonitoring;
import com.walnut.vegaspread.workflow.model.JobExecutionDto;
import com.walnut.vegaspread.workflow.repository.JobExecutionMonitoringRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.jboss.logging.Logger;

@ApplicationScoped
public class JobExecutionMonitoringService {
    private static final Logger logger = Logger.getLogger(JobExecutionMonitoringService.class);
    private final JobExecutionMonitoringRepository jobExecutionMonitoringRepository;

    public JobExecutionMonitoringService(JobExecutionMonitoringRepository jobExecutionMonitoringRepository) {
        this.jobExecutionMonitoringRepository = jobExecutionMonitoringRepository;
    }

    @Transactional
    public JobExecutionMonitoring auditStartJob(JobExecutionDto.StartJob jobExecutionStartDto) {
        JobExecutionMonitoring jobExecutionMonitoring = JobExecutionMonitoring.builder()
                .jobId(jobExecutionStartDto.jobId())
                .docId(jobExecutionStartDto.docId())
                .stage(jobExecutionStartDto.stage())
                .startTime(jobExecutionStartDto.startTime())
                .endTime(null)
                .isSuccess(null)
                .build();
        jobExecutionMonitoringRepository.persist(jobExecutionMonitoring);
        return jobExecutionMonitoring;
    }

    @Transactional
    public JobExecutionMonitoring auditEndJob(JobExecutionDto.EndJob jobExecutionEndDto) {
        JobExecutionMonitoring jobExecutionMonitoring = jobExecutionMonitoringRepository.findById(
                jobExecutionEndDto.jobId());
        if (jobExecutionMonitoring == null) {
            logger.errorf("Start job monitoring record not found for job id %s", jobExecutionEndDto.jobId());
            throw new IllegalArgumentException(
                    "Start job monitoring record not found for job id " + jobExecutionEndDto.jobId());
        }
        jobExecutionMonitoring.setEndTime(jobExecutionEndDto.endTime());
        jobExecutionMonitoring.setIsSuccess(jobExecutionEndDto.isSuccess());
        jobExecutionMonitoringRepository.persist(jobExecutionMonitoring);
        return jobExecutionMonitoring;
    }
}
