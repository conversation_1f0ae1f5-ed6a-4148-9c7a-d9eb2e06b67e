package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.model.ApiKeyCredentialsDto;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.utils.Config;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import java.net.URI;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.walnut.vegaspread.workflow.service.WiseWorkflowService.JOB_MONITORING_ENDPOINT;

@ApplicationScoped
public class ProcessorService {
    private static final Logger logger = Logger.getLogger(ProcessorService.class);
    private final DocumentService documentService;
    private final ExchangeService exchangeService;
    private final CloudProvider cloudProvider;
    @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY)
    String envName;
    @ConfigProperty(name = Config.WISE_CLIENT_SECRET_KEY)
    String clientSecret;
    @ConfigProperty(name = Config.API_CLIENT_NAME)
    String clientName;

    public ProcessorService(DocumentService documentService, ExchangeService exchangeService,
                            CloudProviderFactory cloudProviderFactory) {
        this.documentService = documentService;
        this.exchangeService = exchangeService;
        this.cloudProvider = cloudProviderFactory.getCloudProvider();
    }

    private static StageEnum getCbStage(StageEnum processStage) {
        return switch (processStage) {
            case ROTATION -> StageEnum.OCR;
            case OCR -> StageEnum.DOC_AI;
            case DOC_AI -> StageEnum.FS_CLF;
            case FS_CLF -> StageEnum.PROCESS;
            case PROCESS, FINISH_REVIEW, REPROCESS_COA, REPROCESS_COMPLETE, REPROCESS_TABLE_TAG, REPROCESS_NTA_BLOCK ->
                    StageEnum.FINISH_PROCESS;
            default -> throw new IllegalArgumentException("Invalid process stage: " + processStage);
        };
    }

    public Response.Status startProcess(UUID docId, StageEnum processStage, URI baseUri) {
        DocOutputDto doc;
        if (processStage.equals(StageEnum.FINISH_REVIEW)) {
            doc = documentService.docToOutput(documentService.getFromDb(docId));
        } else {
            doc = documentService.updateDocStatus(docId, StatusEnum.PROCESSING, processStage.toString());
        }
        if (doc == null) {
            return Response.Status.NOT_FOUND;
        }

        StageEnum cbStage = getCbStage(processStage);
        WiseWorkflowService wiseWorkflowService = getWiseWorkflowService(baseUri, doc, cbStage, processStage);
        wiseWorkflowService.startProcess(processStage);
        exchangeService.createDocAudit(
                List.of(new DocumentAuditDto.Create(docId, "PROCESSING", processStage.toString())));
        return Response.Status.OK;
    }

    public Response.Status processBlock(UUID docId, URI baseUri, Integer blockId) throws InterruptedException {
        DocOutputDto doc = documentService.docToOutput(documentService.getFromDb(docId));
        StageEnum processStage = StageEnum.REPROCESS_NTA_BLOCK;
        StageEnum cbStage = getCbStage(processStage);
        WiseWorkflowService wiseWorkflowService = getWiseWorkflowService(baseUri, doc, cbStage, processStage);
        wiseWorkflowService.startReprocessBlock(blockId);
        exchangeService.createDocAudit(
                List.of(new DocumentAuditDto.Create(docId, "PROCESSING_BLOCK", blockId.toString())));
        return Response.Status.OK;
    }

    private WiseWorkflowService getWiseWorkflowService(URI baseUri, DocOutputDto doc, StageEnum cbStage,
                                                       StageEnum errorStage) {
        String bucketName = cloudProvider.getBucketName();

        String startJobMonitoringCallbackUrl = baseUri.toString() + JOB_MONITORING_ENDPOINT + "/" + "start-job";
        logger.debugf("Starting job monitoring callback url: %s", startJobMonitoringCallbackUrl);

        String endJobMonitoringCallbackUrl = baseUri + JOB_MONITORING_ENDPOINT + "/" + "end-job";
        logger.debugf("End job monitoring callback url: %s", endJobMonitoringCallbackUrl);

        WiseCallbackDto.CbMetadata cbMetadata = new WiseCallbackDto.CbMetadata(doc.docId(),
                doc.createdTime().toLocalDate(), bucketName, Objects.equals(doc.documentType(), "Digital"), cbStage, 0,
                StringUtils.EMPTY, errorStage);

        ApiKeyCredentialsDto credentialsDto = new ApiKeyCredentialsDto(this.clientName, this.clientSecret);
        return new WiseWorkflowService(cbMetadata, credentialsDto, baseUri, cloudProvider, this.envName, doc);
    }
}
