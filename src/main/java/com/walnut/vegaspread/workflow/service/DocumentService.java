package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import com.walnut.vegaspread.common.model.audit.workflow.ReviewAuditDto;
import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.Review;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.DocFile;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.ListTaskDto;
import com.walnut.vegaspread.workflow.model.PutSignedLinkDto;
import com.walnut.vegaspread.workflow.model.ReviewStatusEnum;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.model.TaskListResponseDto;
import com.walnut.vegaspread.workflow.model.UpdateDocDto;
import com.walnut.vegaspread.workflow.model.UpdateReviewerDto;
import com.walnut.vegaspread.workflow.model.UpdateUiDocDto;
import com.walnut.vegaspread.workflow.repository.DocumentRepository;
import com.walnut.vegaspread.workflow.repository.ReviewRepository;
import com.walnut.vegaspread.workflow.utils.OutputFormatter;
import com.walnut.vegaspread.workflow.utils.QueryBuilder;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.Logger;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import static com.walnut.vegaspread.workflow.utils.Config.WALNUT_AI_USER;
import static com.walnut.vegaspread.workflow.utils.Utils.getCurrentReviewer;
import static com.walnut.vegaspread.workflow.utils.Utils.isRevised;

@ApplicationScoped
public class DocumentService {

    public static final String ORIGINAL_DOC_SUFFIX = "_original";
    public static final String PDF_MIME_TYPE = "application/pdf";
    private static final Logger logger = Logger.getLogger(DocumentService.class);
    private static final Integer PUT_SIGNED_URL_TIMEOUT = 3;
    private final DocumentRepository documentRepository;
    private final TaskService taskService;
    private final OutputFormatter outputFormatter;
    private final ReviewRepository reviewRepository;
    private final ExchangeService exchangeService;
    private final ProcessorService processorService;
    private final CloudProvider cloudProvider;
    private final EzeeCallbackService ezeeCallbackService;

    public DocumentService(DocumentRepository documentRepository, TaskService taskService,
                           ReviewRepository reviewRepository, ExchangeService exchangeService,
                           ProcessorService processorService,
                           CloudProviderFactory cloudProviderFactory,
                           EzeeCallbackService ezeeCallbackService) {
        this.documentRepository = documentRepository;
        this.taskService = taskService;
        this.reviewRepository = reviewRepository;
        this.exchangeService = exchangeService;
        this.outputFormatter = new OutputFormatter(exchangeService);
        this.processorService = processorService;
        this.cloudProvider = cloudProviderFactory.getCloudProvider();
        this.ezeeCallbackService = ezeeCallbackService;
    }

    private Document getDocument(UUID docId) {
        Optional<Document> document = documentRepository.findByIdOptional(docId);
        if (document.isEmpty()) {
            logger.errorf("Document with id {} not found", docId);
            return null;
        }
        return documentRepository.findById(docId);
    }

    private void updateLastModified(Document document, String username) {
        LocalDateTime currentTime = LocalDateTime.now();
        document.setLastModifiedBy(username);
        document.setLastModifiedTime(currentTime);
        document.getSpreadingTask().setLastModifiedBy(username);
        document.getSpreadingTask().setLastModifiedTime(currentTime);
    }

    @Transactional
    public long deleteDoc(UUID docId) {
        Document document = documentRepository.findById(docId);
        long deleteCount = 0;
        if (document != null) {
            cloudProvider.delete(document.getFilePath());
            deleteCount = documentRepository.delete("docId", docId);
        }
        if (deleteCount != 1) {
            logger.errorf("Error deleting document {} ", docId);
        }
        return deleteCount;
    }

    @Transactional
    public DocOutputDto updateDocStatus(UUID docId, StatusEnum status, String statusText) {
        Document document = getDocument(docId);
        if (document == null) {
            return null;
        }
        StatusEnum oldStatus = document.getStatus();
        document.setStatus(status);
        document.setStatusText(statusText);
        documentRepository.persist(document);

        // Send EZEE callback if status changed
        if (!status.equals(oldStatus)) {
            ezeeCallbackService.sendStatusChangeCallback(docId, document.getApplicationId(), status);
        }

        return docToOutputForApi(document);
    }

    public DocOutputDto docToOutput(Document document) {
        return outputFormatter.taskToOutput(document);
    }

    public DocOutputDto docToOutputForApi(Document document) {
        return outputFormatter.taskToOutputForApi(document);
    }

    @Transactional
    public void updateInDb(Document document) {
        documentRepository.persist(document);
    }

    private Document createNewDocInDb(Integer spreadId, String fileName, Long fileSize, String username,
                                      String applicationId) {
        Document document = Document.builder()
                .fileName(fileName)
                .filePath("")
                .fileSize(Math.round((float) fileSize / 1024))
                .auditable(new Auditable(username))
                .status(StatusEnum.DRAFT)
                .statusText(StatusEnum.DRAFT.toString())
                .isOutlier(false)
                .outlierComment(StringUtils.EMPTY)
                .applicationId(applicationId)
                .lastModifiedBy(username)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        if (spreadId > 0) {
            document.setSpreadingTask(taskService.get(spreadId));
        }
        documentRepository.persist(document);

        return document;
    }

    @Transactional
    public Document uploadDoc(Integer spreadId, DocFile fileUpload, String username, boolean audit,
                              String applicationId) throws IOException {
        Document document = createNewDocInDb(spreadId, fileUpload.fileName(), fileUpload.fileSize(), username,
                applicationId);
        UUID docId = document.getDocId();

        String objectPath = String.format("%s/%s.pdf", LocalDate.now(), docId);
        cloudProvider.upload(objectPath, fileUpload.filePath());
        document.setFilePath(objectPath);
        documentRepository.persist(document);

        if (audit) {
            List<DocumentAuditDto.Create> auditDto = List.of(
                    new DocumentAuditDto.Create(docId, Document.STATUS_COL_NAME, document.getStatus().toString()),
                    new DocumentAuditDto.Create(docId, Document.LAST_MODIFIED_BY_COL_NAME,
                            document.getLastModifiedBy()));
            exchangeService.createDocAudit(auditDto);
        }
        return document;
    }

    @Transactional
    public PutSignedLinkDto generateUploadLinkForNewDoc(Integer spreadId, String fileName, Integer fileSize,
                                                        String username, String applicationId) {
        Document document = createNewDocInDb(spreadId, fileName, Long.valueOf(fileSize), username, applicationId);
        UUID docId = document.getDocId();
        String objectPath = String.format("%s/%s.pdf", LocalDate.now(), docId);
        document.setFilePath(objectPath);
        documentRepository.persist(document);
        URL uploadUrl = cloudProvider.getSignedPutUrl(objectPath, PUT_SIGNED_URL_TIMEOUT, PDF_MIME_TYPE);

        List<DocumentAuditDto.Create> auditDto = List.of(
                new DocumentAuditDto.Create(docId, Document.STATUS_COL_NAME, document.getStatus().toString()),
                new DocumentAuditDto.Create(docId, Document.LAST_MODIFIED_BY_COL_NAME,
                        document.getLastModifiedBy()));
        exchangeService.createDocAudit(auditDto);
        return new PutSignedLinkDto(uploadUrl, document.getDocId());
    }

    @Transactional
    public DocOutputDto updateInDbByProcessor(UUID docId, UpdateDocDto updateDocDto, String username) {
        Document document = getDocument(docId);
        if (document == null) {
            return null;
        }

        if (updateDocDto.dpi() != null) {
            document.setDpi(updateDocDto.dpi().shortValue());
        }
        if (updateDocDto.mappedItems() != null) {
            document.setMappedItems(updateDocDto.mappedItems().shortValue());
        }
        if (updateDocDto.ocrScore() != null) {
            document.setOcrScore(updateDocDto.ocrScore().byteValue());
        }
        StatusEnum oldStatus = document.getStatus();
        if (updateDocDto.status() != null) {
            logger.debugf("Updating status to {}", updateDocDto.status());
            document.setStatus(updateDocDto.status());
            document.setStatusText(updateDocDto.status().toString());
            if (document.getStatus().equals(StatusEnum.UNDER_REVIEW_LVL_1)) {
                ArrayList<Review> newReviews = new ArrayList<>();
                if (document.getReviews().isEmpty()) {
                    Review review = Review.builder()
                            .status(ReviewStatusEnum.PENDING)
                            .document(document)
                            .reviewer(document.getAuditable().getCreatedBy())
                            .reviewLevel(1)
                            .reviewedTime(LocalDateTime.now())
                            .build();
                    newReviews.add(review);
                } else {
                    Review aiReview = Review.builder()
                            .status(ReviewStatusEnum.REASSIGNED)
                            .document(document)
                            .reviewer(WALNUT_AI_USER)
                            .reviewLevel(1)
                            .reviewedTime(LocalDateTime.now())
                            .build();
                    String lastReviewer = getCurrentReviewer(document);
                    Review userReview = Review.builder()
                            .status(ReviewStatusEnum.PENDING)
                            .document(document)
                            .reviewer(lastReviewer)
                            .reviewLevel(1)
                            .reviewedTime(LocalDateTime.now())
                            .build();
                    newReviews.addAll(List.of(aiReview, userReview));
                }
                reviewRepository.persist(newReviews);
            }

            // Send EZEE callback if status changed
            if (!updateDocDto.status().equals(oldStatus)) {
                ezeeCallbackService.sendStatusChangeCallback(docId, document.getApplicationId(), updateDocDto.status());
            }
        }

        updateLastModified(document, username);
        documentRepository.persist(document);

        return docToOutputForApi(document);
    }

    @Transactional
    public LocalDateTime updateTimeInDb(UUID docId, String username) {
        LocalDateTime currentTime = LocalDateTime.now();
        Document document = getDocument(docId);
        if (document == null) {
            return null;
        }
        DocumentAuditDto.UpdateOrDelete updateDocAuditDto = new DocumentAuditDto.UpdateOrDelete(docId,
                Document.LAST_MODIFIED_BY_COL_NAME, document.getLastModifiedBy(), username);
        updateLastModified(document, username);
        documentRepository.persist(document);
        exchangeService.updateDocAudit(List.of(updateDocAuditDto));
        return currentTime;
    }

    private Object getNewValue(UiUpdateField field, UpdateUiDocDto docDto) {
        return switch (field) {
            case STATUS -> docDto.status();
            case PERIOD -> docDto.period();
            case SPREAD_LEVEL -> docDto.spreadLevel();
            case IS_DIGITAL -> docDto.isDigital();
            case FILE_DENOMINATION -> docDto.fileDenomination();
            case OUTPUT_DENOMINATION -> docDto.outputDenomination();
        };
    }

    private Object getCurrentValue(UiUpdateField field, Document currentDoc) {
        return switch (field) {
            case STATUS -> currentDoc.getStatus();
            case PERIOD -> currentDoc.getPeriod();
            case SPREAD_LEVEL -> currentDoc.getSpreadLevel();
            case IS_DIGITAL -> currentDoc.getIsDigital();
            case FILE_DENOMINATION -> currentDoc.getFileDenomination();
            case OUTPUT_DENOMINATION -> currentDoc.getOutputDenomination();
        };
    }

    private void handleCompletedStatus(Document document, UpdateUiDocDto docDto, URI baseUri) {
        document.setIsOutlier(docDto.isOutlier() != null && docDto.isOutlier());
        document.setOutlierComment(docDto.outlierComment());
        processorService.startProcess(document.getDocId(), StageEnum.FINISH_REVIEW, baseUri);
    }

    private void updateDocumentValue(UiUpdateField field, Document document, UpdateUiDocDto docDto, URI baseUri) {
        switch (field) {
            case STATUS -> {
                StatusEnum oldStatus = document.getStatus();
                document.setStatus(docDto.status());
                document.setStatusText(docDto.status().toString());
                if (docDto.status().equals(StatusEnum.COMPLETED)) {
                    handleCompletedStatus(document, docDto, baseUri);
                }
                // Send EZEE callback if status changed
                if (!docDto.status().equals(oldStatus)) {
                    ezeeCallbackService.sendStatusChangeCallback(document.getDocId(), document.getApplicationId(),
                            docDto.status());
                }
            }
            case PERIOD -> document.setPeriod(docDto.period());
            case SPREAD_LEVEL -> document.setSpreadLevel(docDto.spreadLevel());
            case IS_DIGITAL -> document.setIsDigital(docDto.isDigital());
            case FILE_DENOMINATION -> document.setFileDenomination(docDto.fileDenomination());
            case OUTPUT_DENOMINATION -> document.setOutputDenomination(docDto.outputDenomination());
        }
    }

    private void addAuditEntry(UiUpdateField field, UUID docId, Object currentValue, Object newValue,
                               List<DocumentAuditDto.UpdateOrDelete> auditChanges) {
        String columnName = switch (field) {
            case STATUS -> Document.STATUS_COL_NAME;
            case PERIOD -> Document.PERIOD_COL_NAME;
            case SPREAD_LEVEL -> Document.SPREAD_LEVEL_COL_NAME;
            case IS_DIGITAL -> Document.IS_DIGITAL_COL_NAME;
            case FILE_DENOMINATION -> Document.FILE_DENOMINATION_COL_NAME;
            case OUTPUT_DENOMINATION -> Document.OUTPUT_DENOMINATION_COL_NAME;
        };

        String oldValue = currentValue != null ? currentValue.toString() : StringUtils.EMPTY;
        String newValueStr = newValue != null ? newValue.toString() : StringUtils.EMPTY;

        auditChanges.add(new DocumentAuditDto.UpdateOrDelete(docId, columnName, oldValue, newValueStr));
    }

    private void updateField(UiUpdateField field, Document document, Document currentDoc, UpdateUiDocDto docDto,
                             List<DocumentAuditDto.UpdateOrDelete> auditChanges, URI baseUri) {
        Object newValue = getNewValue(field, docDto);
        Object currentValue = getCurrentValue(field, currentDoc);

        if (newValue != null && !newValue.equals(currentValue)) {
            updateDocumentValue(field, document, docDto, baseUri);
            addAuditEntry(field, document.getDocId(), currentValue, newValue, auditChanges);
        }
    }

    @Transactional
    public Document updateInDbByUi(UUID docId, UpdateUiDocDto docDto, String clientName, String username, URI baseUri) {
        List<DocumentAuditDto.UpdateOrDelete> updateDocAuditDtos = new ArrayList<>();
        Document document = getDocument(docId);
        if (document == null) {
            return null;
        }
        Document currentDoc = new Document(document, false);

        SpreadingTask currentTask = document.getSpreadingTask();

        if ((docDto.entityName() != null && docDto.industry() != null && docDto.region() != null)
                && (
                !Objects.equals(currentTask.getEntityName().getName(), docDto.entityName().name())
                        || !Objects.equals(currentTask.getIndustry().getIndustryName(),
                        docDto.industry().industryName())
                        || !Objects.equals(currentTask.getRegion().getRegionName(), docDto.region().regionName())
        )
        ) {
            document.setSpreadingTask(
                    taskService.createTaskForUpdateInDbByUi(docDto.entityName(), docDto.industry(), docDto.region(),
                            clientName, username, document));
        }

        for (UiUpdateField field : UiUpdateField.values()) {
            updateField(field, document, currentDoc, docDto, updateDocAuditDtos, baseUri);
        }

        updateDocAuditDtos.add(
                new DocumentAuditDto.UpdateOrDelete(docId, Document.LAST_MODIFIED_BY_COL_NAME,
                        currentDoc.getLastModifiedBy(), username));
        updateLastModified(document, username);
        documentRepository.persist(document);

        exchangeService.updateDocAudit(updateDocAuditDtos);

        return document;
    }

    public Document getFromDb(UUID docId) {
        return documentRepository.findById(docId);
    }

    public UrlDto getSignedUrl(UUID docId, String fileType) {

        Document document = getDocument(docId);
        if (document == null) {
            return null;
        }
        String filePath = document.getFilePath();
        if (fileType != null && fileType.equalsIgnoreCase("original")) {
            filePath = addSuffixToFileName(filePath, ORIGINAL_DOC_SUFFIX);
        }
        return new UrlDto(cloudProvider.getLink(filePath, 30));
    }

    private String addSuffixToFileName(String filePath, String suffix) {
        int dotIndex = filePath.lastIndexOf(".");
        if (dotIndex == -1) {
            return filePath + suffix;
        }
        return filePath.substring(0, dotIndex) + suffix + filePath.substring(dotIndex);
    }

    public List<UUID> filterDocuments(String filterColumn, Integer filterValue, String clientName) {
        List<Integer> spreadIds = taskService.filterSpreadingTask(filterColumn, filterValue, clientName);
        if (spreadIds == null) {
            return List.of();
        }
        return documentRepository.findCompletedDocIdsBySpreadIds(spreadIds);
    }

    public TaskListResponseDto listDocs(ListTaskDto.GetTaskList taskListDto, String clientName) {
        QueryBuilder qb = new QueryBuilder(taskListDto, documentRepository);
        List<Document> docs = qb.getList(clientName);

        // Return total number of tasks after filtering
        int whereIndex = qb.getQuery().indexOf("FROM");
        String countQuery = qb.getQuery().substring(whereIndex);
        long totalTasks = documentRepository.count(countQuery, qb.getParams());
        List<DocOutputDto> tasks = outputFormatter.taskToOutput(docs);
        return new TaskListResponseDto(taskListDto.pageNumber(), taskListDto.pageSize(),
                (int) Math.ceil((double) totalTasks / taskListDto.pageSize()),
                (int) totalTasks, tasks);
    }

    @Transactional
    public DocOutputDto requestReview(UUID docId, String username) {
        Document document = getDocument(docId);
        if (document == null) {
            return null;
        }
        List<ReviewAuditDto.Update> updateReviewDtos = new ArrayList<>();
        List<Review> sortedReviews = document.getReviews().stream()
                .sorted(Comparator.comparing(Review::getReviewedTime).reversed())
                .toList();
        //Update current review status to changes requested.
        Review currentReview = sortedReviews.get(0);
        updateReviewDtos.add(
                new ReviewAuditDto.Update(currentReview.getId(), Review.STATUS_COL_NAME,
                        currentReview.getStatus().toString(),
                        ReviewStatusEnum.CHANGES_REQUESTED.toString()));
        currentReview.setStatus(ReviewStatusEnum.CHANGES_REQUESTED);
        currentReview.setReviewedTime(LocalDateTime.now());
        exchangeService.updateReviewAudit(updateReviewDtos);

        Review updatedReview = Review.builder()
                .status(ReviewStatusEnum.PENDING)
                .document(document)
                .reviewer(sortedReviews.get(1).getReviewer())
                .reviewLevel(1)
                .reviewedTime(LocalDateTime.now())
                .build();
        reviewRepository.persist(List.of(currentReview, updatedReview));
        exchangeService.createReviewAudit(
                List.of(new ReviewAuditDto.Create(updatedReview.id, Review.STATUS_COL_NAME,
                        updatedReview.getStatus().toString())));

        DocumentAuditDto.UpdateOrDelete updateDocAuditDto = new DocumentAuditDto.UpdateOrDelete(docId,
                Document.STATUS_COL_NAME, document.getStatus().toString(), StatusEnum.UNDER_REVIEW_LVL_1.toString());
        StatusEnum oldStatus = document.getStatus();
        document.setStatus(StatusEnum.UNDER_REVIEW_LVL_1);
        document.setStatusText(StatusEnum.UNDER_REVIEW_LVL_1.toString());
        updateLastModified(document, username);
        documentRepository.persist(document);
        exchangeService.updateDocAudit(List.of(updateDocAuditDto));

        // Send EZEE callback for status change
        if (!StatusEnum.UNDER_REVIEW_LVL_1.equals(oldStatus)) {
            ezeeCallbackService.sendStatusChangeCallback(docId, document.getApplicationId(),
                    StatusEnum.UNDER_REVIEW_LVL_1);
        }

        return docToOutput(document);
    }

    @Transactional
    public DocOutputDto requestApproval(UUID docId, String lvl2Reviewer, String username) {
        Document document = getDocument(docId);
        if (document == null || !document.getStatus().equals(StatusEnum.UNDER_REVIEW_LVL_1)
                || document.getReviews().isEmpty() || lvl2Reviewer.isEmpty()) {
            return null;
        }
        //Update status for current review to sent for approval.
        Review recentReview = reviewRepository.findMostRecentlyUpdated(docId);
        ReviewAuditDto.Update updateReviewAuditDto = new ReviewAuditDto.Update(recentReview.id,
                Review.STATUS_COL_NAME, recentReview.getStatus().toString(),
                ReviewStatusEnum.SENT_FOR_APPROVAL.toString());
        recentReview.setReviewedTime(LocalDateTime.now());
        recentReview.setStatus(ReviewStatusEnum.SENT_FOR_APPROVAL);
        reviewRepository.persist(recentReview);
        exchangeService.updateReviewAudit(List.of(updateReviewAuditDto));

        //Create a new review for the next level.
        Review lvl2Review = Review.builder()
                .status(ReviewStatusEnum.PENDING)
                .document(document)
                .reviewer(lvl2Reviewer)
                .reviewLevel(2)
                .reviewedTime(LocalDateTime.now())
                .build();
        reviewRepository.persist(lvl2Review);
        exchangeService.createReviewAudit(
                List.of(new ReviewAuditDto.Create(lvl2Review.id, Review.STATUS_COL_NAME,
                        lvl2Review.getStatus().toString())));

        DocumentAuditDto.UpdateOrDelete updateDocAuditDto = new DocumentAuditDto.UpdateOrDelete(docId,
                Document.STATUS_COL_NAME, document.getStatus().toString(), StatusEnum.UNDER_REVIEW_LVL_2.toString());
        StatusEnum oldStatus = document.getStatus();
        document.setStatus(StatusEnum.UNDER_REVIEW_LVL_2);
        document.setStatusText(StatusEnum.UNDER_REVIEW_LVL_2.toString());
        updateLastModified(document, username);
        documentRepository.persist(document);
        exchangeService.updateDocAudit(List.of(updateDocAuditDto));

        // Send EZEE callback for status change
        if (!StatusEnum.UNDER_REVIEW_LVL_2.equals(oldStatus)) {
            ezeeCallbackService.sendStatusChangeCallback(docId, document.getApplicationId(),
                    StatusEnum.UNDER_REVIEW_LVL_2);
        }

        return docToOutput(document);
    }

    @Transactional
    public Review reassignReviewer(UUID docId, UpdateReviewerDto updateReviewerDto, String username) {
        Document document = getDocument(docId);
        if (document == null || document.getReviews().isEmpty() || updateReviewerDto.newReviewer().isEmpty()) {
            return null;
        }
        Review recentReview = reviewRepository.findMostRecentlyUpdated(docId);
        if (!recentReview.getReviewLevel().equals(updateReviewerDto.reviewLevel())) {
            logger.errorf("Document reviewer cannot be changed for level {} since current review level is {}",
                    updateReviewerDto.reviewLevel(), recentReview.getReviewLevel());
            return null;
        }

        final ReviewStatusEnum currentStatus = recentReview.getStatus();
        recentReview.setStatus(ReviewStatusEnum.REASSIGNED);
        recentReview.setReviewedTime(LocalDateTime.now());
        reviewRepository.persist(recentReview);
        exchangeService.updateReviewAudit(
                List.of(new ReviewAuditDto.Update(recentReview.getId(), Review.STATUS_COL_NAME,
                        currentStatus.toString(), recentReview.getStatus().toString())));

        Review newReview = Review.builder()
                .status(currentStatus)
                .document(recentReview.getDocument())
                .reviewer(updateReviewerDto.newReviewer())
                .reviewLevel(updateReviewerDto.reviewLevel())
                .reviewedTime(LocalDateTime.now())
                .build();
        reviewRepository.persist(newReview);
        exchangeService.createReviewAudit(List.of(new ReviewAuditDto.Create(newReview.getId(), Review.STATUS_COL_NAME,
                newReview.getStatus().toString())));

        updateLastModified(document, username);
        documentRepository.persist(document);
        return newReview;
    }

    @Transactional
    public DocOutputDto approveReview(UUID docId, String username) {
        Document document = getDocument(docId);
        if (document == null || !document.getStatus().equals(StatusEnum.UNDER_REVIEW_LVL_2)
                || document.getReviews().isEmpty()) {
            return null;
        }
        Review recentReview = reviewRepository.findMostRecentlyUpdated(docId);
        ReviewStatusEnum newStatus = isRevised(document)
                ? ReviewStatusEnum.REVISED_APPROVED : ReviewStatusEnum.APPROVED;
        ReviewAuditDto.Update updateReviewAuditDto = new ReviewAuditDto.Update(recentReview.getId(),
                Review.STATUS_COL_NAME, recentReview.getStatus().toString(), newStatus.toString());
        recentReview.setStatus(newStatus);
        recentReview.setReviewedTime(LocalDateTime.now());

        updateLastModified(document, username);
        documentRepository.persist(document);

        reviewRepository.persist(recentReview);
        exchangeService.updateReviewAudit(List.of(updateReviewAuditDto));
        return docToOutput(document);
    }

    @Transactional
    public DocOutputDto reviseReview(UUID docId, String username) {
        Document document = getDocument(docId);
        if (document == null || !document.getStatus().equals(StatusEnum.COMPLETED)
                || document.getReviews().isEmpty()) {
            return null;
        }

        Review recentReview = reviewRepository.findMostRecentlyUpdated(docId);
        if (!List.of(ReviewStatusEnum.APPROVED, ReviewStatusEnum.REVISED_APPROVED).contains(recentReview.getStatus())) {
            logger.errorf("Document cannot be revised since current review status is %s", recentReview.getStatus());
            return null;
        }

        Review reviseReview = Review.builder()
                .status(ReviewStatusEnum.REVISED)
                .document(document)
                .reviewer(username)
                .reviewLevel(recentReview.getReviewLevel())
                .reviewedTime(LocalDateTime.now())
                .build();

        reviewRepository.persist(reviseReview);
        exchangeService.createReviewAudit(List.of(new ReviewAuditDto.Create(reviseReview.getId(),
                Review.STATUS_COL_NAME, reviseReview.getStatus().toString())));

        StatusEnum oldStatus = document.getStatus();
        document.setStatus(StatusEnum.UNDER_REVIEW_LVL_2);
        document.setStatusText(StatusEnum.UNDER_REVIEW_LVL_2.toString());
        updateLastModified(document, username);
        documentRepository.persist(document);

        // Send EZEE callback for status change
        if (!StatusEnum.UNDER_REVIEW_LVL_2.equals(oldStatus)) {
            ezeeCallbackService.sendStatusChangeCallback(docId, document.getApplicationId(),
                    StatusEnum.UNDER_REVIEW_LVL_2);
        }

        return docToOutput(document);
    }

    public List<Review> listReviews(UUID docId) {
        List<Review> reviews = reviewRepository.getReviews(docId);
        Map<String, String> mappings = exchangeService.getUsernameMapping(reviews.stream().map(Review::getReviewer));
        reviews.forEach(
                review -> review.setReviewerFullName(mappings.getOrDefault(review.getReviewer(), StringUtils.EMPTY)));
        return reviews;
    }

    public byte[] download(UUID docId, String fileType) {
        if (docId == null) {
            logger.errorf("Document id is null");
            return new byte[0];
        }
        Document document = documentRepository.findById(docId);
        if (document == null) {
            logger.errorf("Document not found for id %s", docId);
            return new byte[0];
        }
        String filePath = document.getFilePath();
        if (fileType != null && fileType.equalsIgnoreCase("original")) {
            filePath = addSuffixToFileName(filePath, ORIGINAL_DOC_SUFFIX);
        }
        return cloudProvider.download(filePath);
    }

    private enum UiUpdateField {
        STATUS,
        PERIOD,
        SPREAD_LEVEL,
        IS_DIGITAL,
        FILE_DENOMINATION,
        OUTPUT_DENOMINATION
    }
}
