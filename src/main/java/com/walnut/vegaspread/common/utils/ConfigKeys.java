package com.walnut.vegaspread.common.utils;

public class ConfigKeys {
    public static final String ENV_NAME_KEY = "vega.env";
    public static final String CLOUD_PROVIDER_TYPE = "vega.cloud.provider";
    public static final String AWS_GATEWAY_URL = "vega.cloud.api-gateway-url";
    public static final String AWS_REGION = "aws.region";
    public static final String APP_VERSION = "quarkus.application.version";

    private ConfigKeys() {
        throw new IllegalStateException("Utility class");
    }
}
