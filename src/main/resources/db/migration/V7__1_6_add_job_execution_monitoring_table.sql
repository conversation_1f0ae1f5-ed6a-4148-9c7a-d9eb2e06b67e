CREATE TABLE `job_execution_monitoring` (
    `job_id`     VA<PERSON>HA<PERSON>(255) NOT NULL,
    `doc_id`     BINARY(16) NOT NULL,
    `stage`     ENUM(
                      'ROTATION',
                      'OCR',
                      'DOC_AI',
                      'FS_CLF',
                      'PROCESS',
                      'FIN<PERSON>H_REVIEW',
                      '<PERSON><PERSON><PERSON><PERSON><PERSON>_COMPLETE',
                      'REPROCESS_TABLE_TAG',
                      'REP<PERSON>CESS_COA',
                      'REPROCESS_NTA_BLOCK'
                  ) NOT NULL,
    `start_time` DATETIME(6) NOT NULL,
    `end_time`   DATETIME(6),
    `is_success` BOOLEAN,
    PRIMARY KEY (`job_id`),
    <PERSON><PERSON>Y `fk_job_execution_monitoring_doc_id` (`doc_id`),
    CONSTRAINT `fk_job_execution_monitoring_doc_id`
        FOREIGN KEY (`doc_id`)
        REFERENCES `document` (`doc_id`)
        ON DELETE CASCADE
);
