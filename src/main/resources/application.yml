quarkus:
  jackson:
    fail-on-empty-beans: false
  smallrye-openapi:
    security-scheme: oauth2-implicit

  swagger-ui:
    always-include: true
    oauth-client-id: workflow-swagger-ui

  http:
    root-path: /vegaspread/api/v1/workflow
    limits:
      max-form-attribute-size: 250M
      max-body-size: 250M
    access-log:
      enabled: true
    proxy:
      proxy-address-forwarding: true
      enable-forwarded-host: true
      enable-forwarded-prefix: true
    cors:
      enabled: true
      origins: "*"

  flyway:
    migrate-at-start: true

  hibernate-orm:
    physical-naming-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    log:
      sql: false

  rest-client:
    logging:
      scope: request-response
    ezee-callback:
      url: ${EZEE_CALLBACK_URL:https://demolend.ezeefin.net.in/centralAPI/v1}

org:
  eclipse:
    microprofile:
      rest:
        client:
          propagateHeaders: Authorization,X-API-KEY,X-CLIENT-ID

vega:
  env: ${VEGA_ENV}
  retry-count:
    rotation: ${RETRY_COUNT:1}
    ocr: ${RETRY_COUNT:1}
    doc-ai: ${RETRY_COUNT:1}
    fs-clf: ${RETRY_COUNT:1}
    processor: ${RETRY_COUNT:1}


wise:
  client:
    secret: client-secret
