quarkus:
  http:
    port: 8084

  security:
    auth:
      enabled-in-dev-mode: false

  keycloak:
    admin-client:
      server-url: https://auth.vega.internal
      realm: dev-vega
      client-id: admin-cli
      client-secret: SECRET
      grant-type: CLIENT_CREDENTIALS
      tls-configuration-name: vega-tls
    api-key-auth:
      realm: dev-vega

  tls:
    vega-tls:
      trust-store:
        p12:
          path: ssl/vega-truststore.p12
          password: changeit
  oidc:
    auth-server-url: https://auth.vega.internal/realms/dev-vega
    tls:
      tls-configuration-name: vega-tls

  smallrye-openapi:
    oauth2-implicit-authorization-url: https://auth.vega.internal/realms/dev-vega/protocol/openid-connect/auth

  native:
    resources:
      includes: ssl/vega-truststore.p12

vega:
  cloud:
    provider: aws
    api-gateway-url: https://d2s6i6axf2.execute-api.ap-southeast-1.amazonaws.com/test
    region: ${AURORA_INSTANCE_REGION:ap-southeast-1}
  env: local
