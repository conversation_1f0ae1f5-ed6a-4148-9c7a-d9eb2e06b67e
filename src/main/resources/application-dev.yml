quarkus:
  datasource:
    username: root
    jdbc:
      url: ****************************************
      additional-jdbc-properties:
        sslMode=VERIFY_CA
    password: root
  tls:
    oidc-tls:
      trust-store:
        p12:
          path: ssl/vega-truststore.p12
          password: changeit
  oidc:
    auth-server-url: https://auth.vega.internal/realms/dev-vega
    tls:
      tls-configuration-name: oidc-tls
  smallrye-openapi:
    oauth2-implicit-authorization-url: https://auth.vega.internal/realms/dev-vega/protocol/openid-connect/auth
  native:
    resources:
      includes: ssl/vega-truststore.p12

  http:
    port: 8083

vega:
  cloud:
    provider: aws
    api-gateway-url: https://d2s6i6axf2.execute-api.ap-southeast-1.amazonaws.com/test
    region: ${AURORA_INSTANCE_REGION:ap-southeast-1}
  env: dev
