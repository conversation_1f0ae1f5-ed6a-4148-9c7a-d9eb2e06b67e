BUILD_NUMBER=local

ENV=dev
SERVICE_NAME=vector
ENV_UPPER=$(shell echo ${ENV} | tr '[:lower:]' '[:upper:]')
GCLOUD_RUN_CPU=1
GCLOUD_RUN_MEM=256Mi
GCLOUD_RUN_CONCURRENCY=80
GCLOUD_RUN_MAX_INSTANCES=1
BUILD_DATE=$(shell date "+%d-%m-%y")
BUILD_ID=${ENV}-${BUILD_DATE}-${BUILD_NUMBER}
CLOUD_SQL_INSTANCE=vegaspread-7586a:asia-southeast1:vega-db
GCLOUD_RUN_MAX_INSTANCES=1
KEYCLOAK_REALM=dev-vega

AWS_ACCOUNT_ID = $(shell aws sts get-caller-identity --query Account --output text)
LAMBDA_S3_BUCKET_NAME = dev-vega-build-artifacts-${AWS_ACCOUNT_ID}
TRUSTSTORE_PASSWORD=changeit

deploy:
	gcloud artifacts docker images delete asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME} --quiet || true
	docker tag thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME}
	docker push asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME}

	gcloud run deploy ${ENV}-vega-${SERVICE_NAME} \
		--image asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME} \
		--allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
		--max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
		--region=asia-southeast1 --cpu-boost --cpu-throttling \
		--set-env-vars VEGA_ENV=${ENV} \
		--set-env-vars CLOUD_SQL_INSTANCE=${CLOUD_SQL_INSTANCE} \
		--set-env-vars QUARKUS_OIDC_AUTH_SERVER_URL=https://auth.vegaspread.cloud/realms/${KEYCLOAK_REALM} \
		--project=vegaspread-7586a --service-account=${ENV}-<EMAIL>

deploy-native:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth mvn clean package -Pnative,gcp,mysql
	docker build -f src/main/docker/Dockerfile.native-micro -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy

deploy-jvm:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth mvn clean package -Pjvm,gcp,mysql
	docker build -f src/main/docker/Dockerfile.jvm -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy

deploy-native-aws:
	QUARKUS_TLS__VEGA_TLS__TRUST_STORE_P12_PASSWORD=${TRUSTSTORE_PASSWORD} mvn clean package -Pnative,aws,postgres
	@echo "Uploading zip file to S3..."
	aws s3 cp target/function.zip s3://${LAMBDA_S3_BUCKET_NAME}/${SERVICE_NAME}-lambda.zip
	@echo "Updating Lambda function..."
	aws lambda update-function-code \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--s3-bucket ${LAMBDA_S3_BUCKET_NAME} \
		--s3-key ${SERVICE_NAME}-lambda.zip
	@echo "Waiting for Lambda update to complete..."
	aws lambda wait function-updated --function-name ${ENV}-${SERVICE_NAME}-lambda
	@echo "Lambda function updated successfully."

verify-lambda:
	@echo "Verifying Lambda function update..."
	@echo "Last update time (local):"
	@aws lambda get-function \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--query 'Configuration.LastModified' \
		--output text | xargs -I{} date -d {} "+%Y-%m-%d %H:%M:%S local time"
	@echo "Revision ID:"
	@aws lambda get-function \
		--function-name ${ENV}-${SERVICE_NAME}-lambda \
		--query 'Configuration.RevisionId' \
		--output text

